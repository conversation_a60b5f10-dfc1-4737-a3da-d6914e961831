import copy

from records.utils import json_utils

# Data coming to/from ira extractor/timeline
global_client_schema = {
  "$schema": "http://json-schema.org/draft-04/schema#",
  "type": "object",
  "properties": {
    "accounting_method": {
      "type": ["string", "null"]
    },
    "active_since": {
      "type": ["string", "null"]
    },
    "agr_signed": {
      "type": ["string", "null"]
    },
    "agreement_sum": {
      "type": ["number", "null"]
    },
    "billing_method": {
      "type": ["string", "null"]
    },
    "bookkeeping": {
      "type": ["boolean", "null"]
    },
    "name": {
      "type": "string"
    },
    "company_phone": {
      "type": ["string", "null"]
    },
    "cpa": {
      "type": ["string", "null"]
    },
    "description": {
      "type": ["string", "null"]
    },
    "dissolution_date": {
      "type": ["string", "null"]
    },
    "ein": {
      "type": ["string", "null"]
    },
    "fedtaxforms": {
      "type": ["string", "null"]
    },
    "financial_year_end": {
      "type": ["string", "null"]
    },
    "financial_year_end_for_subsidiary": {
      "type": ["string", "null"]
    },
    "incorp_by": {
      "type": ["string", "null"]
    },
    "legal_ent_type": {
      "type": ["string", "null"]
    },
    "login": {
      "type": ["string", "null"]
    },
    "monthly_bill": {
      "type": ["number", "null"]
    },
    "naicscode": {
      "type": ["string", "null"]
    },
    "notes_accounting": {
      "type": ["string", "null"]
    },
    "notes_address": {
      "type": ["string", "null"]
    },
    "notes_agreement": {
      "type": ["string", "null"]
    },
    "notes_contacts": {
      "type": ["string", "null"]
    },
    "notes_main": {
      "type": ["string", "null"]
    },
    "notes_shareholders": {
      "type": ["string", "null"]
    },
    "optional_share_count": {
      "type": ["integer", "null"]
    },
    "paid_by": {
      "type": ["string", "null"]
    },
    "paid_by_mail": {
      "type": ["string", "null"]
    },
    "password": {
      "type": ["string", "null"]
    },
    "payroll": {
      "type": ["boolean", "null"]
    },
    "renewal_date": {
      "type": ["string", "null"]
    },
    "renewal_date_mail": {
      "type": ["string", "null"]
    },
    "since": {
      "type": ["string", "null"]
    },
    "statetaxforms": {
      "type": ["string", "null"]
    },
    "status": {
      "type": ["string", "null"]
    },
    "subjurisd": {
      "type": ["string", "null"]
    },
    "subsidiary_legal_entity_type": {
      "type": ["string", "null"]
    },
    "subsidiary_to_consolidate": {
      "type": ["string", "null"]
    },
    "total_shares": {
      "type": ["integer", "null"]
    },
    "withdrawal_date": {
      "type": ["string", "null"]
    },
    "addresses": {
      "type": "array",
      "items": [
        {
          "type": "object",
          "properties": {
            "address_type": {
              "type": "string"
            },
            "renewal_date": {
              "type": ["string", "null"]
            },
            "phone": {
              "type": ["string", "null"]
            },
            "paid_by": {
              "type": ["string", "null"]
            },
            "note": {
              "type": ["string", "null"]
            },
            "address": {
              "type": ["object", "null"],
              "properties": {
                "street": {
                  "type": "string"
                },
                "country": {
                  "type": "string"
                },
                "city": {
                  "type": ["string", "null"]
                },
                "full_address": {
                  "type": ["string", "null"]
                },
                "pobox": {
                  "type": ["string", "null"]
                },
                "state": {
                  "type": ["string", "null"]
                },
                "zip": {
                  "type": ["string", "null"]
                }
              },
              "required": [
                "full_address",
                "street",
                "pobox",
                "city",
                "state",
                "zip",
                "country"
              ]
            }
          },
          "required": [
            "address_type",
            "renewal_date",
            "phone",
            "paid_by",
            "note",
            "address"
          ]
        }
      ]
    },
    "authorized_signers": {
      "type": "array",
      "items": [
        {
          "type": "object",
          "properties": {
            "note": {
              "type": ["string", "null"]
            },
            "person": {
                "type": "object",
                "properties": {
                    "firstname": {
                        "type": "string"
                    },
                    "lastname": {
                        "type": "string"
                    },
                    "email": {
                        "type": ["string", "null"]
                    },
                    "phone": {
                        "type": ["string", "null"]
                    },
                    "pcm": {
                        "type": ["string", "null"]
                    },
                    "citizenship": {
                        "type": ["string", "null"]
                    },
                    "address": {
                        "type": ["string", "null"]
                    },
                    "companies": {
                        "type": ["string", "null"]
                    }
                },
                "required": [
                    "firstname",
                    "lastname",
                    "email",
                    "phone",
                    "pcm",
                    "citizenship",
                    "address",
                    "companies"
                ]
            }
          },
          "required": [
            "note",
            "person"
          ]
        }
      ]
    },
    "bank_accounts": {
      "type": "array",
      "items": [
        {
          "type": "object",
          "properties": {
            "bank_name": {
              "type": "string"
            },
            "aba_number": {
              "type": "string"
            },
            "account_number": {
              "type": ["string", "null"]
            },
            "bank_contact": {
              "type": ["string", "null"]
            },
            "controlled_by": {
              "type": ["string", "null"]
            },
            "date_opened": {
              "type": ["string", "null"]
            },
            "last_renewal": {
              "type": ["string", "null"]
            },
            "notes": {
              "type": ["string", "null"]
            },
            "authorized_signers": {
                "type": "array",
                "items": [
                    {
                        "type": "object",
                        "properties": {
                            "person": {
                                "type": "object",
                                "properties": {
                                    "firstname": {
                                        "type": "string"
                                    },
                                    "lastname": {
                                        "type": "string"
                                    },
                                    "email": {
                                        "type": ["string", "null"]
                                    },
                                    "phone": {
                                        "type": ["string", "null"]
                                    },
                                    "pcm": {
                                        "type": ["string", "null"]
                                    },
                                    "citizenship": {
                                        "type": ["string", "null"]
                                    },
                                    "address": {
                                        "type": ["string", "null"]
                                    },
                                    "companies": {
                                        "type": ["string", "null"]
                                    }
                                },
                                "required": [
                                    "firstname",
                                    "lastname",
                                    "email",
                                    "phone",
                                    "pcm",
                                    "citizenship",
                                    "address",
                                    "companies"
                                ]
                            }
                        },
                        "required": [
                            "person"
                        ]
                    }
                ]
            },
            "bank_cards": {
                "type": "array",
                "items": [
                    {
                        "type": "object",
                        "properties": {
                            "card_number": {
                                "type": "string"
                            },
                            "last_4_digits": {
                                "type": "string"
                            },
                            "expired_at": {
                                "type": ["string", "null"]
                            },
                            "cvv": {
                                "type": ["string", "null"]
                            },
                            "card_holder_name": {
                                "type": "string"
                            },
                            "valid_through": {
                                "type": ["string", "null"]
                            }
                        },
                        "required": [
                            "card_number",
                            "last_4_digits",
                            "expired_at",
                            "cvv",
                            "card_holder_name",
                            "valid_through"
                        ]
                    }
                ]
            }
          },
          "required": [
            "bank_name",
            "aba_number",
            "account_number",
            "bank_contact",
            "controlled_by",
            "date_opened",
            "last_renewal",
            "notes",
            "authorized_signers",
            "bank_cards"
          ]
        }
      ]
    },
    "contacts": {
      "type": "array",
      "items": [
        {
          "type": "object",
          "properties": {
            "position": {
              "type": "string"
            },
            "email": {
              "type": ["string", "null"]
            },
            "phone": {
              "type": ["string", "null"]
            },
            "pcm": {
              "type": ["string", "null"]
            },
            "note": {
              "type": ["string", "null"]
            },
            "person": {
              "type": "object",
              "properties": {
                "firstname": {
                  "type": "string"
                },
                "lastname": {
                  "type": "string"
                },
                "email": {
                  "type": ["string", "null"]
                },
                "phone": {
                  "type": ["string", "null"]
                },
                "pcm": {
                  "type": ["string", "null"]
                },
                "citizenship": {
                  "type": ["string", "null"]
                },
                "address": {
                  "type": ["string", "null"]
                },
                "companies": {
                  "type": ["string", "null"]
                }
              },
              "required": [
                "firstname",
                "lastname",
                "email",
                "phone",
                "pcm",
                "citizenship",
                "address",
                "companies"
              ]
            }
          },
          "required": [
            "position",
            "email",
            "phone",
            "pcm",
            "note",
            "person"
          ]
        }
      ]
    },
    "payment_cards": {
      "type": "array",
      "items": [
        {
          "type": "object",
          "properties": {
            "debit_card": {
              "type": "string"
            },
            "last_4_digits": {
              "type": "string"
            },
            "expired_at": {
              "type": ["string", "null"]
            },
            "cid": {
              "type": "string"
            },
            "linked_to": {
              "type": ["string", "null"]
            },
            "card_holder": {
              "type": "string"
            },
            "exp": {
              "type": "string"
            }
          },
          "required": [
            "debit_card",
            "last_4_digits",
            "expired_at",
            "cid",
            "linked_to",
            "card_holder",
            "exp"
          ]
        }
      ]
    },
    "payment_services": {
      "type": "array",
      "items": [
        {
          "type": "object",
          "properties": {
            "payment_system": {
              "type": "string"
            },
            "date_opened": {
              "type": ["string", "null"]
            },
            "opened_by": {
              "type": ["string", "null"]
            },
            "email_connected": {
              "type": ["string", "null"]
            },
            "responsible_person": {
              "type": ["string", "null"]
            },
            "note": {
              "type": ["string", "null"]
            }
          },
          "required": [
            "payment_system",
            "date_opened",
            "opened_by",
            "email_connected",
            "responsible_person",
            "note"
          ]
        }
      ]
    },
    "primary_registration": {
      "type": "object",
      "properties": {
        "registered_date": {
          "type": ["string", "null"]
        },
        "terminated_date": {
          "type": ["string", "null"]
        },
        "last_renewal_date": {
          "type": ["string", "null"]
        },
        "annual_compliance_due_date": {
          "type": ["string", "null"]
        },
        "state_of_incorporation": {
          "type": "string"
        },
        "billed_to": {
          "type": ["string", "null"]
        },
        "last_soi_filed": {
          "type": ["string", "null"]
        },
        "state_entity": {
          "type": ["string", "null"]
        },
        "notes": {
          "type": ["string", "null"]
        },
        "registered_agent": {
          "type": ["object", "null"],
          "properties": {
            "address": {
              "type": "object",
              "properties": {
                "street": {
                  "type": ["string", "null"]
                },
                "country": {
                  "type": ["string", "null"]
                },
                "city": {
                  "type": ["string", "null"]
                },
                "full_address": {
                  "type": ["string", "null"]
                },
                "pobox": {
                  "type": ["string", "null"]
                },
                "state": {
                  "type": ["string", "null"]
                },
                "zip": {
                  "type": ["string", "null"]
                }
              },
              "required": [
                "full_address",
                "street",
                "pobox",
                "city",
                "state",
                "zip",
                "country"
              ]
            },
            "title": {
              "type": "string"
            },
            "nickname": {
              "type": ["string", "null"]
            }
          },
          "required": [
            "address",
            "title",
            "nickname"
          ]
        }
      },
      "required": [
        "registered_date",
        "terminated_date",
        "last_renewal_date",
        "annual_compliance_due_date",
        "state_of_incorporation",
        "billed_to",
        "last_soi_filed",
        "state_entity",
        "notes",
        "registered_agent"
      ]
    },
    "secondary_registrations": {
      "type": "array",
      "items": [
        {
          "type": "object",
          "properties": {
            "registered_date": {
              "type": ["string", "null"]
            },
            "terminated_date": {
              "type": ["string", "null"]
            },
            "last_renewal_date": {
              "type": ["string", "null"]
            },
            "annual_compliance_due_date": {
              "type": ["string", "null"]
            },
            "state_of_incorporation": {
              "type": "string"
            },
            "billed_to": {
              "type": ["string", "null"]
            },
            "last_soi_filed": {
              "type": ["string", "null"]
            },
            "state_entity": {
              "type": ["string", "null"]
            },
            "notes": {
              "type": ["string", "null"]
            },
            "registered_agent": {
              "type": ["object", "null"],
              "properties": {
                "address": {
                  "type": "object",
                  "properties": {
                    "street": {
                      "type": ["string", "null"]
                    },
                    "country": {
                      "type": ["string", "null"]
                    },
                    "city": {
                      "type": ["string", "null"]
                    },
                    "full_address": {
                      "type": ["string", "null"]
                    },
                    "pobox": {
                      "type": ["string", "null"]
                    },
                    "state": {
                      "type": ["string", "null"]
                    },
                    "zip": {
                      "type": ["string", "null"]
                    }
                  },
                  "required": [
                    "full_address",
                    "street",
                    "pobox",
                    "city",
                    "state",
                    "zip",
                    "country"
                  ]
                },
                "title": {
                  "type": "string"
                },
                "nickname": {
                  "type": ["string", "null"]
                }
              },
              "required": [
                "address",
                "title",
                "nickname"
              ]
            }
          },
          "required": [
            "registered_date",
            "terminated_date",
            "last_renewal_date",
            "annual_compliance_due_date",
            "state_of_incorporation",
            "billed_to",
            "last_soi_filed",
            "state_entity",
            "notes",
            "registered_agent"
          ]
        }
      ]
    },
    "shares": {
      "type": "array",
      "items": [
        {
          "type": "object",
          "properties": {
            "type": {
              "type": ["string", "null"]
            },
            "stock_authorized": {
              "type": ["integer", "null"]
            },
            "stock_issued": {
              "type": ["integer", "null"]
            },
            "notes": {
              "type": ["string", "null"]
            }
          },
          "required": [
            "type",
            "stock_authorized",
            "stock_issued",
            "notes"
          ]
        }
      ]
    },
    "llc_shareholders": {
      "type": "array",
      "items": [
        {
          "type": "object",
          "properties": {
            "position": {
              "type": "string"
            },
            "is_managing_member": {
              "type": ["boolean", "null"]
            },
            "ownership": {
              "type": "string"
            },
            "note": {
              "type": ["string", "null"]
            }
          },
          "required": [
            "position",
            "is_managing_member",
            "ownership",
            "note"
          ]
        }
      ]
    },
    "capitalization_table": {
      "type": "array",
      "items": {
        "type": "object",
        "properties": {


          "share_amount": {"type": ["integer", "null"]},
          "date": {"type": ["string", "null"]},
          "issued_percentage": {"type": ["number", "null"]},
          "authorized_percentage": {"type": ["number", "null"]},
          "notes": {"type": ["string", "null"]},
          "person": {
            "type": ["object", "null"],
            "properties": {
              "firstname": {"type": "string"},
              "lastname": {"type": "string"},
              "email": {"type": ["string", "null"]},
              "phone": {"type": ["string", "null"]},
              "pcm": {"type": ["string", "null"]},
              "citizenship": {"type": ["string", "null"]},
              "address": {"type": ["string", "null"]},
              "companies": {"type": ["string", "null"]}
            },
            "required": ["firstname", "lastname"]
          },
          "share": {
            "type": ["object", "null"],
            "properties": {

              "type": {"type": ["string", "null"]},
              "stock_authorized": {"type": ["integer", "null"]},
              "stock_issued": {"type": ["integer", "null"]},
              "notes": {"type": ["string", "null"]}
            }
          }
        }
      }
    },
  },
  "required": [
    "accounting_method",
    "active_since",
    "agr_signed",
    "agreement_sum",
    "billing_method",
    "bookkeeping",
    "name",
    "company_phone",
    "cpa",
    "description",
    "dissolution_date",
    "ein",
    "fedtaxforms",
    "financial_year_end",
    "financial_year_end_for_subsidiary",
    "incorp_by",
    "legal_ent_type",
    "monthly_bill",
    "naicscode",
    "notes_accounting",
    "notes_address",
    "notes_agreement",
    "notes_contacts",
    "notes_main",
    "notes_shareholders",
    "optional_share_count",
    "paid_by",
    "paid_by_mail",
    "password",
    "payroll",
    "renewal_date",
    "renewal_date_mail",
    "since",
    "statetaxforms",
    "status",
    "subjurisd",
    "subsidiary_legal_entity_type",
    "subsidiary_to_consolidate",
    "total_shares",
    "withdrawal_date",
    "addresses",
    "authorized_signers",
    "bank_accounts",
    "capitalization_table",
    "contacts",
    "payment_cards",
    "payment_services",
    "primary_registration",
    "secondary_registrations",
    "shares",
    "llc_shareholders"
  ]
}

# Only for API/Approved data/manual update
_api_client_schema_part = {
  "properties": {
    "manager_id": {"type": ["string", "null"]},
    "manager": {
      "type": ["object", "null"],
      "properties": {
        "id": {"type": ["string", "null"]},
        "user_id": {"type": ["integer", "null"]},
        "title": {"type": ["string", "null"]},
        "email": {"type": ["string", "null"]},
        "phone": {"type": ["string", "null"]},
        "role_name": {"type": ["string", "null"]},
      },
      "required": ["title", "email", "role_name"]
    },
    "services": {
      "type": "array",
      "items": {
        "type": "object",
        "properties": {
          "id": {"type": ["integer", "null"]},
          "service_id": {"type": "string"},
          "active_since": {"type": ["string", "null"]},
          "active_until": {"type": ["string", "null"]},
          "note": {"type": ["string", "null"]},
          "discount_percent": {"type": ["integer", "null"]},
          "discount_amount": {"type": ["string", "null"]},
          "total": {"type": ["string", "null"]},
          "service": {
            "type": "object",
            "properties": {
                "title": {"type": "string"},
                "price": {"type": "number"},
                "price_type": {"type": "string"},
            },
            "required": ["title", "price", "price_type"]
          },
        },
        "required": ["service"]
      }
    },
    "source_id": {"type": ["string", "null"]},
    "source": {
        "type": ["object", "null"],
        "properties": {
            "title": {"type": ["string", "null"]},
        },
    },
    "tasks": {
      "type": "array",
      "items": {
        "type": "object",
        "properties": {
          "date": {"type": ["string", "null"]},
          "name": {"type": ["string", "null"]},
          "due_date": {"type": ["string", "null"]},
          "status": {"type": ["string", "null"]},
          "manager_id": {"type": ["string", "null"]},
          "manager": {
            "type": ["object", "null"],
            "properties": {
              "id": {"type": ["string", "null"]},
              "user_id": {"type": ["integer", "null"]},
              "title": {"type": ["string", "null"]},
              "email": {"type": ["string", "null"]},
              "phone": {"type": ["string", "null"]},
              "role_name": {"type": ["string", "null"]},
            },
            "required": ["title", "email", "role_name"]
          }
        },
        "required": ["date", "name", "due_date", "status", "manager_id", "manager"]
      }
    }
  },
  "required": [
    "manager",
    "services",
    "source",
    "tasks"
  ]
}

api_client_schema = copy.deepcopy(global_client_schema)
api_client_schema = json_utils.dict_merge(api_client_schema, _api_client_schema_part, mode='overwrite_append')

client_report_schema = {
  "$schema": "http://json-schema.org/draft-04/schema#",
  "type": "object",
  "properties": {
    "Legal Corporate Documents": {
      "type": "object",
      "properties": {
        "Filed Certificate of Incorporation": {
          "type": "object",
          "properties": {
            "Company Name": {"type": "string"},
            "Legal entity type": {"type": "string"},
            "State of registration": {"type": "string"},
            "File Number": {"type": "string"},
            "Filed Date": {"type": "string"},
            "Registered office address": {"type": "string"},
            "Registered agent name": {"type": "string"},
            "Number of authorized shares": {"type": "integer"},
            "Par value per share": {"type": "string"}
          },
          "required": [
            "Company Name",
            "Legal entity type",
            "State of registration",
            "File Number",
            "Filed Date",
            "Registered office address",
            "Registered agent name",
            "Number of authorized shares",
            "Par value per share"
          ]
        },
        "Action by Incorporator - Appointing Initial Directors": {
          "type": "object",
          "properties": {
            "List of initial directors": {
              "type": "array",
              "items": {
                "type": "object",
                "properties": {
                  "Name": {"type": "string"},
                  "Address": {"type": "string"}
                },
                "required": [
                  "Name",
                  "Address"
                ]
              }
            },
            "Incorporator": {
              "type": "object",
              "properties": {
                "Name": {"type": "string"},
                "Address": {"type": "string"}
              },
              "required": [
                "Name",
                "Address"
              ]
            }
          },
          "required": [
            "List of initial directors",
            "Incorporator"
          ]
        },
        "Action by Anonymous Written Consent of the Board of Directors": {
          "type": "object",
          "properties": {
            "Corporate Officers": {
              "type": "object",
              "properties": {
                "President Name": {"type": "string"},
                "Treasurer Name": {"type": "string"},
                "Secretary Name": {"type": "string"}
              },
              "required": [
                "President Name",
                "Treasurer Name",
                "Secretary Name"
              ]
            },
            "Stock issuance": {
              "type": "array",
              "items": {
                "type": "object",
                "properties": {
                  "Name": {"type": "string"},
                  "Amount of Stock": {"type": "integer"},
                  "Purchase price": {"type": "string"}
                },
                "required": [
                  "Name",
                  "Amount of Stock",
                  "Purchase price"
                ]
              }
            }
          },
          "required": [
            "Corporate Officers",
            "Stock issuance"
          ]
        },
        "Bylaws": {
          "type": "object",
          "properties": {
            "Board seats number": {
              "type": "integer"
            }
          },
          "required": [
            "Board seats number"
          ]
        },
        "Form SS-4": {
          "type": "object",
          "properties": {
            "Mailing Address": {"type": "string"},
            "Legal type": {"type": "string"},
            "Financial Year End (Closing month of accounting year)": {"type": "string"},
            "Principal activity": {"type": "string"},
            "Products or services sold": {"type": "string"},
            "Tax ID / EIN (Employer Identification Number)": {"type": "string"}
          },
          "required": [
            "Mailing Address",
            "Legal type",
            "Financial Year End (Closing month of accounting year)",
            "Principal activity",
            "Products or services sold",
            "Tax ID / EIN (Employer Identification Number)"
          ]
        },
        "CP575 - Reply to SS-4 - Letter from IRS assigning Tax ID and reporting form": {
          "type": "object",
          "properties": {
            "tax_id": {"type": "string"},
            "assigned_date": {"type": "string", "format": "date"},
            "letter_content": {"type": "string"}
          },
          "required": ["tax_id", "assigned_date"]
        },
        "147c Confirmation of Tax ID number": {
          "type": "object",
          "properties": {
            "confirmation_date": {"type": "string", "format": "date"},
            "confirmation_number": {"type": "string"},
            "issuer": {"type": "string"}
          },
          "required": ["confirmation_date", "confirmation_number"]
        }
      },
      "required": [
        "Filed Certificate of Incorporation",
        "Action by Incorporator - Appointing Initial Directors",
        "Action by Anonymous Written Consent of the Board of Directors",
        "Bylaws",
        "Form SS-4",
        "CP575 - Reply to SS-4 - Letter from IRS assigning Tax ID and reporting form",
        "147c Confirmation of Tax ID number"
      ]
    },
    "Personal Documents (required from Directors and major Shareholders)": {
      "type": "object",
      "properties": {
        "Passport": {
          "type": "array",
          "items": {
            "type": "object",
            "properties": {
              "Name": {"type": "string"},
              "Passport No.": {"type": "string"},
              "Nationality": {"type": "string"},
              "Date of birth": {"type": "string"},
              "Date of issue": {"type": "string"},
              "Date of expiry": {"type": "string"}
            },
            "required": [
              "Name",
              "Passport No.",
              "Nationality",
              "Date of birth",
              "Date of issue",
              "Date of expiry"
            ]
          }
        },
        "Residency permit": {
          "type": "object",
          "properties": {
            "Permit Number": {"type": "string"},
            "Issuing Authority": {"type": "string"},
            "Date of Issue": {"type": "string"},
            "Date of Expiry": {"type": "string"}
          },
          "required": [
            "Permit Number",
            "Issuing Authority",
            "Date of Issue",
            "Date of Expiry"
          ]
        },
        "Verification of Address": {
          "type": "object",
          "properties": {
            "Sublease": {
              "type": "object",
              "properties": {
                "Lease Agreement": {"type": "string"},
                "Lease Start Date": {"type": "string", "format": "date"},
                "Lease End Date": {"type": "string", "format": "date"},
                "Landlord Name": {"type": "string"}
              },
              "required": []
            },
            "Utility Bill": {
              "type": "object",
              "properties": {
                "Bill Document": {"type": "string"},
                "Bill Date": {"type": "string", "format": "date"},
                "Billing Address": {"type": "string"},
                "Provider": {"type": "string"}
              },
              "required": []
            }
          },
          "required": [],
        },
        "SSN Card": {
          "type": "object",
          "properties": {
            "SSN Number": {"type": "string"},
            "Issuing Authority": {"type": "string"},
            "Date of Issue": {"type": "string", "format": "date"}
          },
          "required": [
            "SSN Number",
            "Issuing Authority",
            "Date of Issue"
          ]
        }
      },
      "required": [
        "Passport",
      ]
    }
  },
  "required": [
    "Legal Corporate Documents",
    "Personal Documents (required from Directors and major Shareholders)"
  ]
}
