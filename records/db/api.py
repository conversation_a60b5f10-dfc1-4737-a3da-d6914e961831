from typing import List

import sqlalchemy as sa
from sqlalchemy import text

from records.db import base
# noinspection PyUnresolvedReferences
from records.db.addresses import *
# noinspection PyUnresolvedReferences
from records.db.authorized_signers import *
# noinspection PyUnresolvedReferences
from records.db.bank_accounts import *
# noinspection PyUnresolvedReferences
from records.db.bank_account_auth_signers import *
# noinspection PyUnresolvedReferences
from records.db.bank_account_bank_cards import *
# noinspection PyUnresolvedReferences
from records.db.client_debit_cards import *
# noinspection PyUnresolvedReferences
from records.db.change_records import *
# noinspection PyUnresolvedReferences
from records.db.client_payment_systems import *
# noinspection PyUnresolvedReferences
from records.db.client_services import *
# noinspection PyUnresolvedReferences
from records.db.client_llc_shareholders import *
# noinspection PyUnresolvedReferences
from records.db.client_shares import *
# noinspection PyUnresolvedReferences
from records.db.client_tasks import *
# noinspection PyUnresolvedReferences
from records.db.client_tax_reporting import *
# noinspection PyUnresolvedReferences
from records.db.clients import *
# noinspection PyUnresolvedReferences
from records.db.client_addresses import *
# noinspection PyUnresolvedReferences
from records.db.client_capitalizations import *
# noinspection PyUnresolvedReferences
from records.db.client_contacts import *
# noinspection PyUnresolvedReferences
from records.db.client_extractors import *
# noinspection PyUnresolvedReferences
from records.db.client_files import *
# noinspection PyUnresolvedReferences
from records.db.client_persons import *
# noinspection PyUnresolvedReferences
from records.db.client_registrations import *
# noinspection PyUnresolvedReferences
from records.db.client_timelines import *
# noinspection PyUnresolvedReferences
from records.db.common_extractor import *
# noinspection PyUnresolvedReferences
from records.db.debit_cards import *
# noinspection PyUnresolvedReferences
from records.db.external_ids import *
# noinspection PyUnresolvedReferences
from records.db.catalogs import *
# noinspection PyUnresolvedReferences
from records.db.managers import *
# noinspection PyUnresolvedReferences
from records.db.reg_agents import *
from records.policies import policies
# noinspection PyUnresolvedReferences
from records.db.service_components import *
# noinspection PyUnresolvedReferences
from records.db.sessions import *
# noinspection PyUnresolvedReferences
from records.db.sources import *
# noinspection PyUnresolvedReferences
from records.db.users import *
# noinspection PyUnresolvedReferences
from records.db.user_confirms import *
# noinspection PyUnresolvedReferences
from records.db.user_invites import *
# noinspection PyUnresolvedReferences
from records.db.user_service_accounts import *


def setup_db():
    base.get_engine()


@base.session_aware()
async def lock(id: int, session=None):
    sql = text('SELECT pg_advisory_lock(:id);')
    params = {'id': id}
    res = await session.execute(sql, params)


@base.session_aware()
async def unlock(id: int, session=None):
    sql = text('SELECT pg_advisory_unlock(:id);')
    params = {'id': id}
    res = await session.execute(sql, params)


def get_roles() -> List[dict]:
    user = [policies.AccessName.READ_ALL]
    dev = [policies.AccessName.ALL_CHATS_VIEW,
           policies.AccessName.ALL_CHATS_MANAGE,
           policies.AccessName.FILE_MANAGE]
    owner = [policies.AccessName.OWNER]

    all_permissions = [('user', user), ('developer', dev), ('owner', owner)]
    roles = []
    for i, (name, perm) in enumerate(all_permissions):
        roles.append({
            'id': i + 1,
            'name': name,
            'permissions': policies.encode_permissions(perm, org=True),
        })

    return roles


def get_role_by_id(role_id: int) -> dict | None:
    # 1 - user, 2 - dev, 3 - owner
    roles = get_roles()
    for role in roles:
        if role['id'] == role_id:
            # role['permissions'] = policies.encode_permissions(role['permissions'], org=True)
            return role
    return None


def get_role_by_name(role_name: str):
    roles = get_roles()
    for role in roles:
        if role['name'] == role_name:
            # role['permissions'] = policies.encode_permissions(role['permissions'], org=True)
            return role
    return None
